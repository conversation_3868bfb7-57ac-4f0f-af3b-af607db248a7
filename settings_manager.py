import json
import os
from pathlib import Path

class SettingsManager:
    def __init__(self, error_callback=None):
        """Initialize the settings manager"""
        self.settings_dir = Path.home() / '.image_crawler'
        self.settings_file = self.settings_dir / 'settings.json'
        self.default_settings = {
            'download_dir': str(Path.home() / 'Downloads' / 'ImageCrawler'),
            'max_depth': 2,
            'max_images': 100,
            'concurrent_downloads': 5,
            'min_width': 100,
            'min_height': 100,
            'file_types': ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'],
            'follow_redirects': True,
            'respect_robots': True,
            'use_selenium': False,
            'window_geometry': '1400x900',
            'theme': 'dark',
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'timeout': 30,
            'retry_attempts': 3,
            'duplicate_handling': 'skip',  # skip, rename, overwrite
            'filename_pattern': 'original',  # original, hash, sequential
            'create_subfolders': True,
            'subfolder_pattern': 'domain',  # domain, date, category
            'download_history': [],
            'blocked_domains': [],
            'preferred_domains': [],
            'proxy_settings': {
                'enabled': False,
                'http': '',
                'https': '',
                'auth': {
                    'username': '',
                    'password': ''
                }
            },
            'advanced': {
                'enable_javascript': True,
                'wait_for_lazy_load': 2,
                'scroll_to_load': True,
                'max_scroll_attempts': 5,
                'custom_headers': {},
                'cookies': {},
                'referrer_policy': 'no-referrer-when-downgrade'
            }
        }
        
        # Error callback for reporting errors to GUI or logger
        self.error_callback = error_callback
        
        # Ensure settings directory exists
        self.settings_dir.mkdir(parents=True, exist_ok=True)
        
    def report_error(self, message):
        suggestion = None
        msg_lower = message.lower()
        if "permission" in msg_lower or "access" in msg_lower:
            suggestion = "Check file or folder permissions."
        elif "disk" in msg_lower or "space" in msg_lower:
            suggestion = "Check available disk space."
        elif "json" in msg_lower or "decode" in msg_lower:
            suggestion = "The settings file may be corrupted. Try resetting settings."
        if self.error_callback:
            try:
                self.error_callback('ERROR', message, suggestion)
            except Exception as e:
                print(f"Error in error_callback: {e}")
                print(message)
        else:
            print(message)

    def load_settings(self):
        """Load settings from file or create with defaults"""
        if self.settings_file.exists():
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    # Merge with defaults to handle new settings
                    settings = self.default_settings.copy()
                    settings.update(saved_settings)
                    return settings
            except Exception as e:
                self.report_error(f"Error loading settings: {e}")
                return self.default_settings.copy()
        else:
            # Create settings file with defaults
            self.save_settings(self.default_settings)
            return self.default_settings.copy()
            
    def save_settings(self, settings):
        """Save settings to file"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=4)
            return True
        except Exception as e:
            self.report_error(f"Error saving settings: {e}")
            return False
            
    def update_setting(self, key, value):
        """Update a specific setting"""
        settings = self.load_settings()
        
        # Handle nested keys (e.g., 'proxy_settings.enabled')
        if '.' in key:
            keys = key.split('.')
            current = settings
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            current[keys[-1]] = value
        else:
            settings[key] = value
            
        return self.save_settings(settings)
        
    def get_setting(self, key, default=None):
        """Get a specific setting"""
        settings = self.load_settings()
        
        # Handle nested keys
        if '.' in key:
            keys = key.split('.')
            current = settings
            for k in keys:
                if isinstance(current, dict) and k in current:
                    current = current[k]
                else:
                    return default
            return current
        else:
            return settings.get(key, default)
            
    def reset_settings(self):
        """Reset all settings to defaults"""
        return self.save_settings(self.default_settings)
        
    def export_settings(self, filepath):
        """Export settings to a file"""
        settings = self.load_settings()
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=4)
            return True
        except Exception as e:
            self.report_error(f"Error exporting settings: {e}")
            return False
            
    def import_settings(self, filepath):
        """Import settings from a file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
                # Validate settings structure
                if self.validate_settings(imported_settings):
                    return self.save_settings(imported_settings)
                else:
                    return False
        except Exception as e:
            self.report_error(f"Error importing settings: {e}")
            return False
            
    def validate_settings(self, settings):
        """Validate settings structure"""
        # Basic validation - ensure it's a dictionary
        if not isinstance(settings, dict):
            return False
            
        # You can add more specific validation here
        required_keys = ['download_dir', 'max_depth', 'max_images']
        for key in required_keys:
            if key not in settings:
                return False
                
        return True
        
    def add_to_history(self, url, stats):
        """Add a crawl to download history"""
        settings = self.load_settings()
        history_entry = {
            'url': url,
            'timestamp': stats.get('timestamp'),
            'images_downloaded': stats.get('downloaded', 0),
            'total_size': stats.get('total_size', '0 B'),
            'duration': stats.get('duration', 0)
        }
        
        # Keep only last 100 entries
        settings['download_history'].insert(0, history_entry)
        settings['download_history'] = settings['download_history'][:100]
        
        return self.save_settings(settings)
        
    def get_history(self):
        """Get download history"""
        return self.get_setting('download_history', [])
        
    def clear_history(self):
        """Clear download history"""
        return self.update_setting('download_history', [])
        
    def add_blocked_domain(self, domain):
        """Add a domain to the blocked list"""
        settings = self.load_settings()
        if domain not in settings['blocked_domains']:
            settings['blocked_domains'].append(domain)
            return self.save_settings(settings)
        return True
        
    def remove_blocked_domain(self, domain):
        """Remove a domain from the blocked list"""
        settings = self.load_settings()
        if domain in settings['blocked_domains']:
            settings['blocked_domains'].remove(domain)
            return self.save_settings(settings)
        return True
        
    def is_domain_blocked(self, domain):
        """Check if a domain is blocked"""
        blocked_domains = self.get_setting('blocked_domains', [])
        return domain in blocked_domains

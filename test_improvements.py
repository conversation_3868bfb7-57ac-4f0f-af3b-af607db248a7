#!/usr/bin/env python3
"""
Test script to verify the improvements made to the Image Crawler application.
Tests critical bug fixes, performance improvements, and new features.
"""

import unittest
import tempfile
import shutil
import os
import asyncio
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Import our modules
from input_validator import InputValidator
from error_recovery import ErrorRecoveryManager, ErrorType, RecoveryStrategy
from crawler_engine import ImageCrawler, RateLimiter
from settings_manager import SettingsManager


class TestInputValidator(unittest.TestCase):
    """Test the input validation system"""
    
    def test_url_validation(self):
        """Test URL validation"""
        # Valid URLs
        valid_urls = [
            "https://example.com",
            "http://test.org/path",
            "https://subdomain.example.com:8080/path?query=1"
        ]
        
        for url in valid_urls:
            is_valid, error, suggestion = InputValidator.validate_url(url)
            self.assertTrue(is_valid, f"URL {url} should be valid")
            
        # Invalid URLs
        invalid_urls = [
            "",
            "not-a-url",
            "ftp://example.com",
            "https://",
            "javascript:alert('xss')"
        ]
        
        for url in invalid_urls:
            is_valid, error, suggestion = InputValidator.validate_url(url)
            self.assertFalse(is_valid, f"URL {url} should be invalid")
    
    def test_directory_validation(self):
        """Test directory path validation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Valid directory
            is_valid, error, suggestion = InputValidator.validate_directory_path(temp_dir)
            if not is_valid:
                print(f"Directory validation failed: {error}")
            self.assertTrue(is_valid, f"Valid directory should pass validation: {error}")

            # Invalid characters (Windows)
            invalid_path = os.path.join(temp_dir, "invalid<>path")
            is_valid, error, suggestion = InputValidator.validate_directory_path(invalid_path)
            self.assertFalse(is_valid)
    
    def test_numeric_validation(self):
        """Test numeric input validation"""
        # Valid numbers
        is_valid, value, error, suggestion = InputValidator.validate_numeric_input(
            "5", min_val=1, max_val=10, input_type=int, field_name="Test"
        )
        self.assertTrue(is_valid)
        self.assertEqual(value, 5)
        
        # Out of range
        is_valid, value, error, suggestion = InputValidator.validate_numeric_input(
            "15", min_val=1, max_val=10, input_type=int, field_name="Test"
        )
        self.assertFalse(is_valid)
        
        # Invalid format
        is_valid, value, error, suggestion = InputValidator.validate_numeric_input(
            "not-a-number", min_val=1, max_val=10, input_type=int, field_name="Test"
        )
        self.assertFalse(is_valid)
    
    def test_file_types_validation(self):
        """Test file types validation"""
        # Valid file types
        is_valid, types, error, suggestion = InputValidator.validate_file_types("jpg,png,gif")
        self.assertTrue(is_valid)
        self.assertEqual(types, ['jpg', 'png', 'gif'])
        
        # With dots and spaces
        is_valid, types, error, suggestion = InputValidator.validate_file_types(".jpg, .png, gif")
        self.assertTrue(is_valid)
        self.assertEqual(types, ['jpg', 'png', 'gif'])
        
        # Invalid characters
        is_valid, types, error, suggestion = InputValidator.validate_file_types("jpg,png,gif@#$")
        self.assertFalse(is_valid)


class TestErrorRecovery(unittest.TestCase):
    """Test the error recovery system"""
    
    def setUp(self):
        self.error_manager = ErrorRecoveryManager(max_retries=3)
    
    def test_error_classification(self):
        """Test error classification"""
        # Network error
        network_error = ConnectionError("Connection failed")
        error_type = self.error_manager.classify_error(network_error)
        self.assertEqual(error_type, ErrorType.NETWORK_ERROR)
        
        # Timeout error
        timeout_error = TimeoutError("Request timed out")
        error_type = self.error_manager.classify_error(timeout_error)
        self.assertEqual(error_type, ErrorType.TIMEOUT_ERROR)
        
        # Permission error
        permission_error = PermissionError("Access denied")
        error_type = self.error_manager.classify_error(permission_error)
        self.assertEqual(error_type, ErrorType.PERMISSION_ERROR)
    
    def test_retry_logic(self):
        """Test retry logic"""
        from error_recovery import ErrorContext
        
        # Should retry for network errors
        context = ErrorContext(
            error_type=ErrorType.NETWORK_ERROR,
            original_exception=ConnectionError("test"),
            attempt_number=1
        )
        self.assertTrue(self.error_manager.should_retry(context))
        
        # Should not retry after max attempts
        context.attempt_number = 5
        self.assertFalse(self.error_manager.should_retry(context))
        
        # Should not retry for client errors
        context = ErrorContext(
            error_type=ErrorType.CLIENT_ERROR,
            original_exception=Exception("404 Not Found"),
            attempt_number=1
        )
        self.assertFalse(self.error_manager.should_retry(context))
    
    def test_delay_calculation(self):
        """Test delay calculation for retries"""
        from error_recovery import ErrorContext
        
        context = ErrorContext(
            error_type=ErrorType.NETWORK_ERROR,
            original_exception=ConnectionError("test"),
            attempt_number=1
        )
        
        delay1 = self.error_manager.calculate_delay(context)
        
        context.attempt_number = 2
        delay2 = self.error_manager.calculate_delay(context)
        
        # Delay should increase with backoff
        self.assertGreater(delay2, delay1)


class TestRateLimiter(unittest.TestCase):
    """Test the rate limiting system"""
    
    def setUp(self):
        self.rate_limiter = RateLimiter(requests_per_second=2)
    
    def test_rate_limiting(self):
        """Test that rate limiting works"""
        async def test_rate_limit():
            domain = "example.com"
            
            # First request should be immediate
            start_time = asyncio.get_event_loop().time()
            await self.rate_limiter.wait_if_needed(domain)
            first_request_time = asyncio.get_event_loop().time() - start_time
            
            # Second request should be delayed
            start_time = asyncio.get_event_loop().time()
            await self.rate_limiter.wait_if_needed(domain)
            second_request_time = asyncio.get_event_loop().time() - start_time
            
            # Second request should take longer due to rate limiting
            self.assertGreater(second_request_time, first_request_time)
        
        # Run the async test
        asyncio.run(test_rate_limit())


class TestSettingsManager(unittest.TestCase):
    """Test settings management improvements"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.errors = []
        
        def error_callback(level, msg, suggestion=None):
            self.errors.append((level, msg, suggestion))
            
        self.manager = SettingsManager(error_callback=error_callback)
        self.manager.settings_dir = Path(self.temp_dir)
        self.manager.settings_file = Path(self.temp_dir) / 'settings.json'
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_error_callback_integration(self):
        """Test that error callbacks work properly"""
        # Create invalid JSON file
        with open(self.manager.settings_file, 'w') as f:
            f.write('{invalid json')
        
        # Try to load settings
        settings = self.manager.load_settings()
        
        # Should have called error callback
        self.assertTrue(any('Error loading settings' in msg for level, msg, suggestion in self.errors))
        
        # Should return defaults
        self.assertIn('download_dir', settings)
    
    def test_nested_settings(self):
        """Test nested settings handling"""
        # Test setting nested value
        success = self.manager.update_setting('proxy_settings.enabled', True)
        self.assertTrue(success)
        
        # Test getting nested value
        value = self.manager.get_setting('proxy_settings.enabled')
        self.assertTrue(value)
        
        # Test non-existent nested value
        value = self.manager.get_setting('proxy_settings.nonexistent', 'default')
        self.assertEqual(value, 'default')


class TestMemoryManagement(unittest.TestCase):
    """Test memory management improvements"""
    
    def test_image_cleanup(self):
        """Test that images are properly cleaned up"""
        # This would require integration testing with the actual GUI
        # For now, we'll test the cleanup logic conceptually
        
        gallery_images = [
            {'image': Mock(), 'frame': Mock()},
            {'image': Mock(), 'frame': Mock()}
        ]
        
        # Simulate cleanup
        for item in gallery_images:
            if 'image' in item and item['image']:
                item['image'].close()
            if 'frame' in item and item['frame']:
                item['frame'].destroy()
        
        # Verify cleanup was called
        for item in gallery_images:
            item['image'].close.assert_called_once()
            item['frame'].destroy.assert_called_once()


def run_comprehensive_test():
    """Run all tests and provide a summary"""
    print("🧪 Running comprehensive test suite for Image Crawler improvements...")
    print("=" * 70)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestInputValidator,
        TestErrorRecovery,
        TestRateLimiter,
        TestSettingsManager,
        TestMemoryManagement
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            error_msg = traceback.split('AssertionError: ')[-1].split('\n')[0]
            print(f"  - {test}: {error_msg}")

    if result.errors:
        print("\n🚨 ERRORS:")
        for test, traceback in result.errors:
            error_msg = traceback.split('\n')[-2]
            print(f"  - {test}: {error_msg}")
    
    if not result.failures and not result.errors:
        print("\n✅ All tests passed! The improvements are working correctly.")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_test()
    exit(0 if success else 1)

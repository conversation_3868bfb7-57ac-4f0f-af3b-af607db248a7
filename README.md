# Advanced Image Crawler

A powerful and feature-rich image crawler application with a modern GUI built using Python and CustomTkinter.

## Features

### 🎯 Core Features
- **Multi-threaded crawling** with configurable concurrent downloads
- **Recursive depth crawling** to discover images across linked pages
- **Smart image detection** from various HTML elements (img, source, meta tags, CSS backgrounds)
- **Duplicate prevention** to avoid downloading the same image multiple times
- **robots.txt compliance** option
- **JavaScript support** via Selenium for dynamic websites

### 🖼️ Image Handling
- **Multiple format support**: JPG, PNG, GIF, WebP, BMP
- **Size filtering**: Set minimum width/height requirements
- **Smart filename generation** with collision handling
- **Image validation** before saving

### 🎨 Modern GUI
- **Dark theme** with beautiful CustomTkinter interface
- **Real-time progress monitoring** with statistics
- **Image gallery** with thumbnail preview
- **Built-in image viewer** with editing capabilities:
  - Zoom, pan, rotate, flip
  - Brightness, contrast, saturation adjustments
  - Save edited images
  - Copy to clipboard

### 📊 Monitoring & Control
- **Live download queue** visualization
- **Detailed logging** with export capability
- **Pause/Resume** functionality
- **Statistics tracking**: download speed, success rate, file sizes
- **Download history** persistence

### ⚙️ Advanced Options
- **Proxy support** with authentication
- **Custom headers** and cookies
- **User-agent customization**
- **Configurable timeouts and retries**
- **Domain blocking/allowing**

## Installation

1. Clone or download this repository:
```bash
git clone https://github.com/yourusername/image-crawler.git
cd image-crawler
```

2. Install required dependencies:
```bash
pip install -r requirements.txt
```

3. For Windows users who want clipboard functionality, also install:
```bash
pip install pywin32
```

## Usage

1. Run the application:
```bash
python image_crawler_app.py
```

2. Enter the target URL in the URL field
3. Select or create a download directory
4. Configure crawling parameters:
   - **Max Depth**: How many levels deep to crawl (1-5)
   - **Max Images**: Maximum number of images to download
   - **Concurrent Downloads**: Number of simultaneous downloads (1-10)
   
5. Set image filters (optional):
   - Enable minimum size filter and set width/height
   - Specify allowed file types

6. Configure additional options:
   - Follow Redirects
   - Respect robots.txt
   - Use Selenium for JavaScript sites

7. Click "▶️ Start Crawling" to begin

## Keyboard Shortcuts

### Main Application
- `Ctrl+S` - Start crawling
- `Ctrl+Q` - Quit application
- `Enter` (in URL field) - Start crawling

### Image Viewer
- `Ctrl++` - Zoom in
- `Ctrl+-` - Zoom out
- `Ctrl+0` - Actual size (1:1)
- `Ctrl+F` - Fit to window
- `Ctrl+S` - Save as
- `Ctrl+C` - Copy to clipboard
- `Delete` - Delete image
- `Left Arrow` - Rotate left
- `Right Arrow` - Rotate right
- `Escape` - Close viewer

## Requirements

- Python 3.8 or higher
- Windows, macOS, or Linux
- Chrome/Chromium browser (for Selenium support)

## Configuration

Settings are automatically saved to `~/.image_crawler/settings.json` and include:
- Default download directory
- Crawler parameters
- Window preferences
- Download history
- Blocked domains

## Tips

1. **For JavaScript-heavy sites**: Enable "Use Selenium" option
2. **For large crawls**: Start with a lower max depth and increase if needed
3. **For specific image types**: Use the file type filter to focus on desired formats
4. **For better organization**: Enable subfolder creation in advanced settings

## Troubleshooting

### Selenium not working
- Ensure Chrome/Chromium is installed
- The app will automatically download the appropriate ChromeDriver

### Images not downloading
- Check if the site requires authentication
- Try enabling JavaScript support
- Verify the site isn't blocking automated requests

### Performance issues
- Reduce concurrent downloads
- Lower the max depth setting
- Enable size filtering to skip small images

## License

This project is provided as-is for educational and personal use.

## Acknowledgments

Built with:
- [CustomTkinter](https://github.com/TomSchimansky/CustomTkinter) - Modern UI framework
- [Beautiful Soup](https://www.crummy.com/software/BeautifulSoup/) - HTML parsing
- [Pillow](https://python-pillow.org/) - Image processing
- [Selenium](https://selenium-python.readthedocs.io/) - Web automation
- [aiohttp](https://docs.aiohttp.org/) - Async HTTP client

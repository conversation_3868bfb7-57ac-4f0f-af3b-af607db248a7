"""
Input validation utilities for the Image Crawler application.
Provides comprehensive validation for URLs, file paths, and numeric inputs.
"""

import os
import re
import validators
from urllib.parse import urlparse
from pathlib import Path


class InputValidator:
    """Comprehensive input validation for the image crawler application"""
    
    # URL validation patterns
    URL_PATTERN = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    # File extension patterns
    IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff', '.ico', '.svg'}
    
    @staticmethod
    def validate_url(url):
        """
        Validate URL format and accessibility
        
        Args:
            url (str): URL to validate
            
        Returns:
            tuple: (is_valid, error_message, suggestion)
        """
        if not url or not isinstance(url, str):
            return False, "URL cannot be empty", "Please enter a valid URL"
            
        url = url.strip()
        
        # Basic format validation
        if not InputValidator.URL_PATTERN.match(url):
            return False, "Invalid URL format", "URL must start with http:// or https://"
            
        # Use validators library for more thorough validation
        if not validators.url(url):
            return False, "Invalid URL format", "Please check the URL format (e.g., https://example.com)"
            
        # Check for common issues
        parsed = urlparse(url)
        
        if not parsed.netloc:
            return False, "URL missing domain", "Please include a domain name (e.g., example.com)"
            
        # Check for suspicious patterns
        if '..' in url or url.count('//') > 1:
            return False, "URL contains suspicious patterns", "Please check for double slashes or relative paths"
            
        return True, "", ""
    
    @staticmethod
    def validate_directory_path(path):
        """
        Validate directory path for download location
        
        Args:
            path (str): Directory path to validate
            
        Returns:
            tuple: (is_valid, error_message, suggestion)
        """
        if not path or not isinstance(path, str):
            return False, "Directory path cannot be empty", "Please select a download directory"
            
        path = path.strip()
        
        try:
            # Convert to Path object for better handling
            path_obj = Path(path)
            
            # Check for invalid characters (Windows) - but allow colon for drive letters
            invalid_chars = '<>"|?*'
            # Allow colon only if it's the second character (drive letter)
            if any(char in path for char in invalid_chars):
                return False, f"Path contains invalid characters: {invalid_chars}", "Remove special characters from the path"

            # Check for colon in wrong position (not drive letter)
            colon_positions = [i for i, char in enumerate(path) if char == ':']
            if colon_positions and any(pos != 1 for pos in colon_positions):
                return False, "Colon (:) can only be used for drive letters", "Use proper path format (e.g., C:\\folder)"
                
            # Check path length (Windows has 260 char limit)
            if len(str(path_obj.resolve())) > 250:
                return False, "Path is too long", "Choose a shorter path or move closer to root directory"
                
            # Check if parent directory exists and is writable
            if path_obj.exists():
                if not path_obj.is_dir():
                    return False, "Path exists but is not a directory", "Choose a different path or remove the existing file"
                if not os.access(path_obj, os.W_OK):
                    return False, "Directory is not writable", "Check permissions or choose a different directory"
            else:
                # Check if we can create the directory
                parent = path_obj.parent
                while not parent.exists() and parent != parent.parent:
                    parent = parent.parent
                    
                if not os.access(parent, os.W_OK):
                    return False, "Cannot create directory (permission denied)", "Check permissions or choose a different location"
                    
            return True, "", ""
            
        except (OSError, ValueError) as e:
            return False, f"Invalid path: {str(e)}", "Please enter a valid directory path"
    
    @staticmethod
    def validate_numeric_input(value, min_val=None, max_val=None, input_type=int, field_name="Value"):
        """
        Validate numeric input with optional range checking
        
        Args:
            value: Input value to validate
            min_val: Minimum allowed value
            max_val: Maximum allowed value
            input_type: Expected type (int or float)
            field_name: Name of the field for error messages
            
        Returns:
            tuple: (is_valid, parsed_value, error_message, suggestion)
        """
        if value is None or value == "":
            return False, None, f"{field_name} cannot be empty", f"Please enter a valid {input_type.__name__}"
            
        try:
            # Convert string to number if needed
            if isinstance(value, str):
                value = value.strip()
                if not value:
                    return False, None, f"{field_name} cannot be empty", f"Please enter a valid {input_type.__name__}"
                    
            parsed_value = input_type(value)
            
            # Range validation
            if min_val is not None and parsed_value < min_val:
                return False, None, f"{field_name} must be at least {min_val}", f"Enter a value >= {min_val}"
                
            if max_val is not None and parsed_value > max_val:
                return False, None, f"{field_name} must be at most {max_val}", f"Enter a value <= {max_val}"
                
            return True, parsed_value, "", ""
            
        except (ValueError, TypeError):
            return False, None, f"{field_name} must be a valid {input_type.__name__}", f"Please enter a numeric value"
    
    @staticmethod
    def validate_file_types(file_types_str):
        """
        Validate file types input
        
        Args:
            file_types_str (str): Comma-separated file extensions
            
        Returns:
            tuple: (is_valid, parsed_types, error_message, suggestion)
        """
        if not file_types_str or not isinstance(file_types_str, str):
            return False, [], "File types cannot be empty", "Enter comma-separated file extensions (e.g., jpg,png,gif)"
            
        try:
            # Parse and clean file types
            types = [t.strip().lower() for t in file_types_str.split(',')]
            types = [t.lstrip('.') for t in types if t]  # Remove leading dots and empty strings
            
            if not types:
                return False, [], "No valid file types found", "Enter at least one file extension"
                
            # Validate each type
            invalid_types = []
            for file_type in types:
                if not re.match(r'^[a-z0-9]+$', file_type):
                    invalid_types.append(file_type)
                    
            if invalid_types:
                return False, [], f"Invalid file types: {', '.join(invalid_types)}", "Use only letters and numbers for file extensions"
                
            return True, types, "", ""
            
        except Exception as e:
            return False, [], f"Error parsing file types: {str(e)}", "Check the format (e.g., jpg,png,gif)"
    
    @staticmethod
    def sanitize_filename(filename):
        """
        Sanitize filename to prevent path traversal and invalid characters
        
        Args:
            filename (str): Original filename
            
        Returns:
            str: Sanitized filename
        """
        if not filename:
            return "unnamed_file"
            
        # Remove path components
        filename = os.path.basename(filename)
        
        # Replace invalid characters
        invalid_chars = '<>:"|?*\\/\x00'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
            
        # Remove leading/trailing dots and spaces
        filename = filename.strip('. ')
        
        # Ensure filename is not empty
        if not filename:
            filename = "unnamed_file"
            
        # Limit length
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext
            
        return filename
    
    @staticmethod
    def validate_crawler_settings(settings):
        """
        Validate all crawler settings at once
        
        Args:
            settings (dict): Dictionary of crawler settings
            
        Returns:
            tuple: (is_valid, errors_dict)
        """
        errors = {}
        
        # Validate URL
        if 'url' in settings:
            is_valid, error, suggestion = InputValidator.validate_url(settings['url'])
            if not is_valid:
                errors['url'] = {'error': error, 'suggestion': suggestion}
                
        # Validate download directory
        if 'download_dir' in settings:
            is_valid, error, suggestion = InputValidator.validate_directory_path(settings['download_dir'])
            if not is_valid:
                errors['download_dir'] = {'error': error, 'suggestion': suggestion}
                
        # Validate numeric settings
        numeric_settings = {
            'max_depth': (1, 10, int, "Max Depth"),
            'max_images': (1, 10000, int, "Max Images"),
            'concurrent_downloads': (1, 20, int, "Concurrent Downloads"),
            'min_width': (1, 10000, int, "Min Width"),
            'min_height': (1, 10000, int, "Min Height")
        }
        
        for key, (min_val, max_val, input_type, field_name) in numeric_settings.items():
            if key in settings:
                is_valid, parsed_val, error, suggestion = InputValidator.validate_numeric_input(
                    settings[key], min_val, max_val, input_type, field_name
                )
                if not is_valid:
                    errors[key] = {'error': error, 'suggestion': suggestion}
                    
        # Validate file types
        if 'file_types' in settings:
            is_valid, parsed_types, error, suggestion = InputValidator.validate_file_types(settings['file_types'])
            if not is_valid:
                errors['file_types'] = {'error': error, 'suggestion': suggestion}
                
        return len(errors) == 0, errors

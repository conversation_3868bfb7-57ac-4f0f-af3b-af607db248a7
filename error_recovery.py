"""
Error recovery and resilience utilities for the Image Crawler application.
Provides comprehensive error handling, retry mechanisms, and recovery strategies.
"""

import asyncio
import time
import logging
from enum import Enum
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass
from collections import defaultdict


class ErrorType(Enum):
    """Classification of different error types"""
    NETWORK_ERROR = "network"
    TIMEOUT_ERROR = "timeout"
    SERVER_ERROR = "server"
    CLIENT_ERROR = "client"
    PERMISSION_ERROR = "permission"
    VALIDATION_ERROR = "validation"
    UNKNOWN_ERROR = "unknown"


class RecoveryStrategy(Enum):
    """Different recovery strategies"""
    RETRY_IMMEDIATE = "retry_immediate"
    RETRY_WITH_BACKOFF = "retry_with_backoff"
    SKIP_AND_CONTINUE = "skip_and_continue"
    PAUSE_AND_RETRY = "pause_and_retry"
    ABORT_OPERATION = "abort_operation"


@dataclass
class ErrorContext:
    """Context information for an error"""
    error_type: ErrorType
    original_exception: Exception
    url: Optional[str] = None
    attempt_number: int = 1
    timestamp: float = None
    additional_info: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()
        if self.additional_info is None:
            self.additional_info = {}


class ErrorRecoveryManager:
    """Manages error recovery strategies and retry logic"""
    
    def __init__(self, max_retries=3, base_delay=1.0, max_delay=60.0, backoff_factor=2.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        
        # Error tracking
        self.error_counts = defaultdict(int)
        self.recent_errors = []
        self.max_recent_errors = 100
        
        # Recovery strategies for different error types
        self.recovery_strategies = {
            ErrorType.NETWORK_ERROR: RecoveryStrategy.RETRY_WITH_BACKOFF,
            ErrorType.TIMEOUT_ERROR: RecoveryStrategy.RETRY_WITH_BACKOFF,
            ErrorType.SERVER_ERROR: RecoveryStrategy.RETRY_WITH_BACKOFF,
            ErrorType.CLIENT_ERROR: RecoveryStrategy.SKIP_AND_CONTINUE,
            ErrorType.PERMISSION_ERROR: RecoveryStrategy.SKIP_AND_CONTINUE,
            ErrorType.VALIDATION_ERROR: RecoveryStrategy.SKIP_AND_CONTINUE,
            ErrorType.UNKNOWN_ERROR: RecoveryStrategy.RETRY_WITH_BACKOFF,
        }
        
        # Callbacks for different recovery actions
        self.recovery_callbacks = {}
        
    def classify_error(self, exception: Exception, url: str = None) -> ErrorType:
        """Classify an error into a specific error type"""
        error_msg = str(exception).lower()
        exception_type = type(exception).__name__.lower()
        
        # Network-related errors
        if any(keyword in error_msg for keyword in ['connection', 'network', 'dns', 'resolve']):
            return ErrorType.NETWORK_ERROR
        if any(keyword in exception_type for keyword in ['connectionerror', 'networkerror']):
            return ErrorType.NETWORK_ERROR
            
        # Timeout errors
        if any(keyword in error_msg for keyword in ['timeout', 'timed out']):
            return ErrorType.TIMEOUT_ERROR
        if any(keyword in exception_type for keyword in ['timeout']):
            return ErrorType.TIMEOUT_ERROR
            
        # Server errors
        if any(keyword in error_msg for keyword in ['server error', '5', 'internal server']):
            return ErrorType.SERVER_ERROR
        if any(keyword in exception_type for keyword in ['servererror']):
            return ErrorType.SERVER_ERROR
            
        # Client errors
        if any(keyword in error_msg for keyword in ['4', 'not found', 'forbidden', 'unauthorized']):
            return ErrorType.CLIENT_ERROR
        if any(keyword in exception_type for keyword in ['clienterror']):
            return ErrorType.CLIENT_ERROR
            
        # Permission errors
        if any(keyword in error_msg for keyword in ['permission', 'access denied', 'forbidden']):
            return ErrorType.PERMISSION_ERROR
        if any(keyword in exception_type for keyword in ['permissionerror']):
            return ErrorType.PERMISSION_ERROR
            
        # Validation errors
        if any(keyword in error_msg for keyword in ['validation', 'invalid', 'malformed']):
            return ErrorType.VALIDATION_ERROR
        if any(keyword in exception_type for keyword in ['validationerror', 'valueerror']):
            return ErrorType.VALIDATION_ERROR
            
        return ErrorType.UNKNOWN_ERROR
    
    def should_retry(self, error_context: ErrorContext) -> bool:
        """Determine if an operation should be retried"""
        if error_context.attempt_number >= self.max_retries:
            return False
            
        strategy = self.recovery_strategies.get(error_context.error_type)
        
        if strategy in [RecoveryStrategy.RETRY_IMMEDIATE, RecoveryStrategy.RETRY_WITH_BACKOFF, RecoveryStrategy.PAUSE_AND_RETRY]:
            return True
            
        return False
    
    def calculate_delay(self, error_context: ErrorContext) -> float:
        """Calculate delay before retry based on error context"""
        strategy = self.recovery_strategies.get(error_context.error_type)
        
        if strategy == RecoveryStrategy.RETRY_IMMEDIATE:
            return 0.0
        elif strategy == RecoveryStrategy.RETRY_WITH_BACKOFF:
            delay = self.base_delay * (self.backoff_factor ** (error_context.attempt_number - 1))
            return min(delay, self.max_delay)
        elif strategy == RecoveryStrategy.PAUSE_AND_RETRY:
            return self.base_delay * 2
        else:
            return 0.0
    
    async def handle_error(self, exception: Exception, operation: Callable, 
                          url: str = None, attempt_number: int = 1, **kwargs) -> Any:
        """Handle an error with appropriate recovery strategy"""
        
        # Create error context
        error_type = self.classify_error(exception, url)
        error_context = ErrorContext(
            error_type=error_type,
            original_exception=exception,
            url=url,
            attempt_number=attempt_number,
            additional_info=kwargs
        )
        
        # Track error
        self.track_error(error_context)
        
        # Determine recovery strategy
        strategy = self.recovery_strategies.get(error_type)
        
        if strategy == RecoveryStrategy.ABORT_OPERATION:
            raise exception
        elif strategy == RecoveryStrategy.SKIP_AND_CONTINUE:
            self.log_error(error_context, "Skipping operation due to unrecoverable error")
            return None
        elif self.should_retry(error_context):
            delay = self.calculate_delay(error_context)
            if delay > 0:
                self.log_error(error_context, f"Retrying in {delay:.1f} seconds (attempt {attempt_number})")
                await asyncio.sleep(delay)
            else:
                self.log_error(error_context, f"Retrying immediately (attempt {attempt_number})")
            
            # Execute retry callback if available
            if 'retry_callback' in self.recovery_callbacks:
                await self.recovery_callbacks['retry_callback'](error_context)
            
            # Retry the operation
            try:
                return await operation(**kwargs)
            except Exception as retry_exception:
                return await self.handle_error(
                    retry_exception, operation, url, attempt_number + 1, **kwargs
                )
        else:
            self.log_error(error_context, "Maximum retries exceeded, giving up")
            raise exception
    
    def track_error(self, error_context: ErrorContext):
        """Track error for analysis and reporting"""
        self.error_counts[error_context.error_type] += 1
        self.recent_errors.append(error_context)
        
        # Keep only recent errors
        if len(self.recent_errors) > self.max_recent_errors:
            self.recent_errors = self.recent_errors[-self.max_recent_errors:]
    
    def log_error(self, error_context: ErrorContext, message: str):
        """Log error with context information"""
        url_info = f" for {error_context.url}" if error_context.url else ""
        full_message = f"{message}{url_info}: {error_context.original_exception}"
        
        # Use callback if available, otherwise print
        if 'log_callback' in self.recovery_callbacks:
            self.recovery_callbacks['log_callback']('ERROR', full_message)
        else:
            print(f"ERROR: {full_message}")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics for reporting"""
        total_errors = sum(self.error_counts.values())
        
        return {
            'total_errors': total_errors,
            'error_counts_by_type': dict(self.error_counts),
            'recent_error_count': len(self.recent_errors),
            'error_rate': len(self.recent_errors) / max(1, total_errors) if total_errors > 0 else 0
        }
    
    def set_recovery_callback(self, callback_type: str, callback: Callable):
        """Set a callback for recovery actions"""
        self.recovery_callbacks[callback_type] = callback
    
    def reset_statistics(self):
        """Reset error tracking statistics"""
        self.error_counts.clear()
        self.recent_errors.clear()


class CircuitBreaker:
    """Circuit breaker pattern implementation for error recovery"""
    
    def __init__(self, failure_threshold=5, recovery_timeout=60, expected_exception=Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
    
    async def call(self, operation: Callable, *args, **kwargs):
        """Execute operation with circuit breaker protection"""
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = await operation(*args, **kwargs)
            self.on_success()
            return result
        except self.expected_exception as e:
            self.on_failure()
            raise e
    
    def on_success(self):
        """Handle successful operation"""
        self.failure_count = 0
        self.state = 'CLOSED'
    
    def on_failure(self):
        """Handle failed operation"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = 'OPEN'

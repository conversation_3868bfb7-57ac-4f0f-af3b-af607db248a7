import unittest
import os
import tempfile
import shutil
from pathlib import Path
from settings_manager import SettingsManager

class TestSettingsManager(unittest.TestCase):
    def setUp(self):
        # Create a temp directory for settings
        self.temp_dir = tempfile.mkdtemp()
        self.settings_file = os.path.join(self.temp_dir, 'settings.json')
        self.errors = []
        def error_callback(level, msg):
            self.errors.append((level, msg))
        self.manager = SettingsManager(error_callback=error_callback)
        self.manager.settings_dir = Path(self.temp_dir)
        self.manager.settings_file = Path(self.settings_file)

    def tearDown(self):
        shutil.rmtree(self.temp_dir)

    def test_load_defaults(self):
        settings = self.manager.load_settings()
        self.assertIn('download_dir', settings)
        self.assertEqual(settings['max_depth'], 2)

    def test_save_and_update(self):
        settings = self.manager.load_settings()
        settings['max_depth'] = 5
        self.manager.save_settings(settings)
        loaded = self.manager.load_settings()
        self.assertEqual(loaded['max_depth'], 5)

    def test_error_callback(self):
        # Simulate error by making settings file unwritable
        with open(self.settings_file, 'w') as f:
            f.write('{bad json')
        _ = self.manager.load_settings()
        self.assertTrue(any('Error loading settings' in msg for level, msg in self.errors))

if __name__ == '__main__':
    unittest.main() 
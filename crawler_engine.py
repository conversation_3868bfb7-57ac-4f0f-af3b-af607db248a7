import asyncio
import aiohttp
import aiofiles
import os
import time
import hashlib
import mimetypes
from urllib.parse import urljoin, urlparse, unquote
from urllib.robotparser import RobotFileParser
from bs4 import BeautifulSoup
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from collections import deque
import re
from PIL import Image
from io import BytesIO
import logging
from aiohttp import ClientTimeout, ClientError, ServerTimeoutError
from bs4.element import Tag

class ImageCrawler:
    def __init__(self, url, download_dir, max_depth=2, max_images=100, 
                 concurrent_downloads=5, file_types=None, min_width=None, 
                 min_height=None, follow_redirects=True, respect_robots=True,
                 use_selenium=False, callback=None, retry_attempts=3):
        """Initialize the image crawler"""
        self.start_url = url
        self.download_dir = download_dir
        self.max_depth = max_depth
        self.max_images = max_images
        self.concurrent_downloads = concurrent_downloads
        self.file_types = file_types or ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp']
        self.min_width = min_width
        self.min_height = min_height
        self.follow_redirects = follow_redirects
        self.respect_robots = respect_robots
        self.use_selenium = use_selenium
        self.callback = callback
        self.retry_attempts = retry_attempts
        
        # State management with thread locks
        self.visited_urls = set()
        self.image_urls = set()
        self.downloaded_images = set()
        self.url_queue = deque()
        self.is_running = False
        self.is_paused = False
        self.pause_event = threading.Event()
        self.pause_event.set()
        
        # Thread safety locks
        self.url_lock = threading.Lock()
        self.image_lock = threading.Lock()
        self.stats_lock = threading.Lock()
        self.callback_lock = threading.Lock()
        
        # Statistics
        self.start_time = None
        self.images_found = 0
        self.images_downloaded = 0
        self.images_failed = 0
        self.total_size = 0
        
        # Domain restriction
        parsed = urlparse(url)
        self.allowed_domains = {parsed.netloc}
        
        # Robot parser
        self.robot_parser = None
        if respect_robots:
            self.setup_robot_parser()
            
        # Setup selenium if needed
        self.driver = None
        if use_selenium:
            self.setup_selenium()
            
        # Create download directory
        os.makedirs(download_dir, exist_ok=True)
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
    def setup_robot_parser(self):
        """Setup robots.txt parser"""
        try:
            robot_url = urljoin(self.start_url, '/robots.txt')
            self.robot_parser = RobotFileParser()
            self.robot_parser.set_url(robot_url)
            self.robot_parser.read()
        except Exception as e:
            self.robot_parser = None
            self.log('ERROR', f"Error setting up robot parser: {str(e)}")
            
    def setup_selenium(self):
        """Setup Selenium WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
            chrome_options.add_experimental_option("useAutomationExtension", False)
            
            # Add page load timeout
            chrome_options.page_load_strategy = 'eager'
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(30)
        except WebDriverException as e:
            self.log('ERROR', f"WebDriver error during setup: {str(e)}")
            self.use_selenium = False
        except Exception as e:
            self.log('ERROR', f"Failed to setup Selenium: {str(e)}")
            self.use_selenium = False
            
    def can_fetch(self, url):
        """Check if URL can be fetched according to robots.txt"""
        if not self.respect_robots or not self.robot_parser:
            return True
        try:
            return self.robot_parser.can_fetch("*", url)
        except Exception as e:
            self.log('ERROR', f"Error checking robots.txt: {str(e)}")
            return True
            
    def is_valid_image_url(self, url):
        """Check if URL points to a valid image"""
        # Check file extension
        path = urlparse(url).path.lower()
        if not any(path.endswith(f'.{ext}') for ext in self.file_types):
            return False
            
        # Check if already downloaded
        if url in self.downloaded_images:
            return False
            
        return True
        
    def get_image_urls_from_page(self, url, html_content):
        """Extract image URLs from HTML content"""
        soup = BeautifulSoup(html_content, 'lxml')
        image_urls = set()
        
        # Find images in img tags
        for img in soup.find_all('img'):
            src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')  # type: ignore
            if src:
                absolute_url = urljoin(url, str(src))
                if self.is_valid_image_url(absolute_url):
                    image_urls.add(absolute_url)
                    
        # Find images in source tags (for picture elements)
        for source in soup.find_all('source'):
            srcset = source.get('srcset')  # type: ignore
            if srcset:
                # Parse srcset attribute
                for src in str(srcset).split(','):
                    src_url = src.strip().split()[0]
                    absolute_url = urljoin(url, str(src_url))
                    if self.is_valid_image_url(absolute_url):
                        image_urls.add(absolute_url)
                        
        # Find images in style attributes (background images)
        style_pattern = re.compile(r'url\(["\']?([^"\'()]+)["\']?\)')
        for element in soup.find_all(style=True):
            style = element.get('style', '')  # type: ignore
            matches = style_pattern.findall(style)  # type: ignore
            for match in matches:
                absolute_url = urljoin(url, str(match))
                if self.is_valid_image_url(absolute_url):
                    image_urls.add(absolute_url)
                    
        # Find images in meta tags (og:image, twitter:image)
        for meta in soup.find_all('meta', content=True):
            if not isinstance(meta, Tag):  # Ensure meta is a Tag, not NavigableString
                continue
            if meta.get('property') in ['og:image', 'twitter:image'] or \
               meta.get('name') in ['twitter:image', 'og:image']:
                content = meta.get('content')
                if content:
                    absolute_url = urljoin(url, str(content))
                    if self.is_valid_image_url(absolute_url):
                        image_urls.add(absolute_url)
                        
        return image_urls
        
    def get_page_links(self, url, html_content):
        """Extract links from HTML content"""
        soup = BeautifulSoup(html_content, 'lxml')
        links = set()
        
        for link in soup.find_all('a', href=True):
            href = link['href']  # type: ignore
            absolute_url = urljoin(url, str(href))
            parsed = urlparse(absolute_url)
            
            # Only follow links within allowed domains
            if parsed.netloc in self.allowed_domains:
                # Remove fragment
                clean_url = absolute_url.split('#')[0]
                if clean_url and clean_url not in self.visited_urls:
                    links.add(clean_url)
                    
        return links
        
    async def fetch_page(self, session, url):
        """Fetch a web page with proper timeout and error handling"""
        try:
            if self.use_selenium:
                # Run Selenium in executor to avoid blocking
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(None, self.fetch_with_selenium_sync, url)
            else:
                timeout = ClientTimeout(total=30, connect=10, sock_read=20)
                async with session.get(url, allow_redirects=self.follow_redirects, timeout=timeout) as response:
                    if response.status == 200:
                        return await response.text()
                    elif response.status == 404:
                        self.log('WARNING', f"Page not found (404): {url}")
                    elif response.status >= 500:
                        self.log('ERROR', f"Server error ({response.status}): {url}")
        except ServerTimeoutError:
            self.log('ERROR', f"Server timeout while fetching {url}")
        except asyncio.TimeoutError:
            self.log('ERROR', f"Timeout while fetching {url}")
        except ClientError as e:
            self.log('ERROR', f"Client error fetching {url}: {str(e)}")
        except Exception as e:
            self.log('ERROR', f"Unexpected error fetching {url}: {str(e)}")
        return None
        
    def fetch_with_selenium_sync(self, url):
        """Fetch page using Selenium (for JavaScript-heavy sites) - synchronous version"""
        if not self.driver:
            self.log('ERROR', f"Selenium driver not initialized")
            return None
            
        try:
            self.driver.get(url)
            # Wait for images to load with explicit timeout
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "img"))
                )
            except TimeoutException:
                self.log('WARNING', f"Timeout waiting for images on {url}, proceeding anyway")
            
            # Scroll to trigger lazy loading
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            time.sleep(1)
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(1)
            
            return self.driver.page_source
        except TimeoutException:
            self.log('ERROR', f"Page load timeout for {url}")
            return None
        except WebDriverException as e:
            self.log('ERROR', f"WebDriver error fetching {url}: {str(e)}")
            return None
        except Exception as e:
            self.log('ERROR', f"Unexpected Selenium error for {url}: {str(e)}")
            return None
            
    async def download_image(self, session, url):
        """Download a single image with enhanced error handling, timeouts, and retries"""
        attempt = 0
        while attempt < self.retry_attempts:
            try:
                # Check pause state
                self.pause_event.wait()
                filename = self.generate_filename(url)
                filepath = os.path.join(self.download_dir, filename)
                # Skip if already exists
                if os.path.exists(filepath):
                    self.log('INFO', f"Skipping existing file: {filename}")
                    return
                # Configure timeout for image downloads
                timeout = ClientTimeout(total=60, connect=10, sock_read=50)
                async with session.get(url, timeout=timeout) as response:
                    if response.status == 200:
                        # Read content with size limit (100MB)
                        max_size = 100 * 1024 * 1024
                        content = bytearray()
                        async for chunk in response.content.iter_chunked(8192):
                            content.extend(chunk)
                            if len(content) > max_size:
                                raise ValueError(f"Image too large (>100MB)")
                        
                        content = bytes(content)
                        
                        # Validate image
                        if self.min_width or self.min_height:
                            try:
                                img = Image.open(BytesIO(content))
                                img.verify()  # Verify it's a valid image
                                img = Image.open(BytesIO(content))  # Re-open after verify
                                width, height = img.size
                                
                                if (self.min_width and width < self.min_width) or \
                                   (self.min_height and height < self.min_height):
                                    self.log('INFO', f"Image too small: {url} ({width}x{height})")
                                    return
                            except Exception as e:
                                self.log('WARNING', f"Invalid image format from {url}: {str(e)}")
                                return
                            
                        # Save image atomically
                        temp_filepath = filepath + '.tmp'
                        async with aiofiles.open(temp_filepath, 'wb') as f:
                            await f.write(content)
                        
                        # Rename to final path
                        os.replace(temp_filepath, filepath)
                        
                        file_size = len(content)
                        
                        # Thread-safe statistics update
                        with self.stats_lock:
                            self.total_size += file_size
                            self.images_downloaded += 1
                        
                        with self.image_lock:
                            self.downloaded_images.add(url)
                        
                        # Callback with thread safety
                        if self.callback:
                            self.safe_callback('image_downloaded', {
                                'url': url,
                                'path': filepath,
                                'size': self.format_size(file_size),
                                'type': os.path.splitext(filename)[1][1:].upper()
                            })
                        
                        self.log('SUCCESS', f"Downloaded: {filename}")
                        return  # Success, exit retry loop
                    else:
                        self.log('ERROR', f"HTTP {response.status} for {url}")
                        with self.stats_lock:
                            self.images_failed += 1
                        break  # Don't retry on HTTP error
            except asyncio.CancelledError:
                raise
            except (ServerTimeoutError, asyncio.TimeoutError, ClientError, Exception) as e:
                attempt += 1
                if attempt < self.retry_attempts:
                    self.log('WARNING', f"Download failed for {url} (attempt {attempt}/{self.retry_attempts}): {str(e)}. Retrying...")
                    await asyncio.sleep(1)
                else:
                    with self.stats_lock:
                        self.images_failed += 1
                    if self.callback:
                        self.safe_callback('image_failed', {
                            'url': url,
                            'error': str(e)
                        })
                    self.log('ERROR', f"Failed to download {url} after {self.retry_attempts} attempts: {str(e)}")
                    break
            
    def generate_filename(self, url):
        """Generate a unique filename for the image"""
        parsed = urlparse(url)
        path = unquote(parsed.path)
        
        # Get filename from URL
        filename = os.path.basename(path)
        
        # If no filename, generate from URL hash
        if not filename or '.' not in filename:
            ext = 'jpg'  # Default extension
            # Try to guess extension from content type
            try:
                response = requests.head(url, timeout=5)
                content_type = response.headers.get('content-type', '')
                if content_type.startswith('image/'):
                    ext = content_type.split('/')[-1].split(';')[0]
            except Exception as e:
                self.log('WARNING', f"Could not determine image extension for {url}: {str(e)}")
                
            # Generate filename from URL hash
            url_hash = hashlib.md5(url.encode()).hexdigest()[:10]
            filename = f"image_{url_hash}.{ext}"
            
        # Ensure unique filename
        base, ext = os.path.splitext(filename)
        counter = 1
        while os.path.exists(os.path.join(self.download_dir, filename)):
            filename = f"{base}_{counter}{ext}"
            counter += 1
            
        return filename
        
    async def crawl_page(self, session, url, depth):
        """Crawl a single page for images and links"""
        if depth > self.max_depth or url in self.visited_urls:
            return
            
        if not self.can_fetch(url):
            self.log('INFO', f"Robots.txt disallows: {url}")
            return
            
        self.visited_urls.add(url)
        self.log('INFO', f"Crawling: {url} (depth: {depth})")
        
        # Fetch page
        html_content = await self.fetch_page(session, url)
        if not html_content:
            return
            
        # Extract image URLs with thread safety
        image_urls = self.get_image_urls_from_page(url, html_content)
        for img_url in image_urls:
            with self.image_lock:
                if img_url not in self.image_urls:
                    self.image_urls.add(img_url)
                    with self.stats_lock:
                        self.images_found += 1
                    
                    if self.callback:
                        self.safe_callback('image_found', {
                            'url': img_url,
                            'type': os.path.splitext(urlparse(img_url).path)[1][1:].upper() or 'Unknown'
                        })
                    
                    # Check if we've found enough images
                    if self.images_found >= self.max_images:
                        return
                    
        # Extract and queue links for further crawling with thread safety
        if depth < self.max_depth:
            links = self.get_page_links(url, html_content)
            for link in links:
                with self.url_lock:
                    if link not in self.visited_urls:
                        self.url_queue.append((link, depth + 1))
                    
    async def crawl_worker(self, session):
        """Worker coroutine for crawling pages"""
        while self.is_running and self.url_queue:
            try:
                url, depth = self.url_queue.popleft()
                await self.crawl_page(session, url, depth)
                
                # Update progress
                if self.callback and self.images_found > 0:
                    with self.stats_lock:
                        progress = min(100, (self.images_downloaded / min(self.images_found, self.max_images)) * 100)
                        elapsed = time.time() - (self.start_time or 0)
                        speed = self.images_downloaded / elapsed if elapsed > 0 else 0
                    
                    self.safe_callback('progress_update', {
                        'progress': int(progress),
                        'speed': speed
                    })
                    
            except Exception as e:
                self.log('ERROR', f"Crawl worker error: {str(e)}")
                
    async def download_worker(self, session, download_queue):
        """Worker coroutine for downloading images"""
        while self.is_running:
            try:
                # Check pause state
                self.pause_event.wait()
                
                url = await download_queue.get()
                if url is None:  # Sentinel value
                    break
                    
                await self.download_image(session, url)
                
                # Check if we've downloaded enough
                if self.images_downloaded >= self.max_images:
                    self.is_running = False
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.log('ERROR', f"Download worker error: {str(e)}")
                
    async def run(self):
        """Main crawler coroutine with enhanced session configuration"""
        self.start_time = time.time()
        self.is_running = True
        
        # Initialize URL queue
        self.url_queue.append((self.start_url, 0))
        
        # Create download queue
        download_queue = asyncio.Queue(maxsize=self.concurrent_downloads * 2)
        
        # Configure connection pool and timeout
        connector = aiohttp.TCPConnector(
            limit=self.concurrent_downloads * 2,
            limit_per_host=self.concurrent_downloads,
            force_close=True
        )
        
        timeout = ClientTimeout(total=300)  # 5 minute total timeout for session
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # Start crawl workers
            crawl_tasks = []
            for _ in range(min(3, self.concurrent_downloads)):
                task = asyncio.create_task(self.crawl_worker(session))
                crawl_tasks.append(task)
                
            # Start download workers
            download_tasks = []
            for _ in range(self.concurrent_downloads):
                task = asyncio.create_task(self.download_worker(session, download_queue))
                download_tasks.append(task)
                
            # Queue images for download as they're found
            last_size = 0
            while self.is_running and (self.url_queue or any(not task.done() for task in crawl_tasks)):
                # Queue new images for download
                new_images = list(self.image_urls)[last_size:]
                last_size = len(self.image_urls)
                
                for img_url in new_images:
                    if self.images_downloaded + download_queue.qsize() >= self.max_images:
                        break
                    await download_queue.put(img_url)
                    
                await asyncio.sleep(0.1)
                
            # Wait for remaining downloads
            self.log('INFO', "Finishing remaining downloads...")
            
            # Send sentinel values to stop download workers
            for _ in download_tasks:
                await download_queue.put(None)
                
            # Wait for all tasks to complete
            await asyncio.gather(*crawl_tasks, *download_tasks, return_exceptions=True)
            
        # Cleanup
        if self.driver:
            self.driver.quit()
            
        # Final callback
        if self.callback:
            with self.stats_lock:
                stats = {
                    'total_found': self.images_found,
                    'downloaded': self.images_downloaded,
                    'failed': self.images_failed,
                    'total_size': self.format_size(self.total_size),
                    'duration': time.time() - self.start_time
                }
            self.safe_callback('crawl_complete', stats)
            
        self.log('SUCCESS', f"Crawl complete! Downloaded {self.images_downloaded} images")
        
    def start(self):
        """Start the crawler"""
        try:
            asyncio.run(self.run())
        except Exception as e:
            self.log('ERROR', f"Crawler error: {str(e)}")
            if self.callback:
                self.safe_callback('crawl_complete', {
                    'error': str(e)
                })
                
    def stop(self):
        """Stop the crawler"""
        self.is_running = False
        self.pause_event.set()
        
    def pause(self):
        """Pause the crawler"""
        self.is_paused = True
        self.pause_event.clear()
        
    def resume(self):
        """Resume the crawler"""
        self.is_paused = False
        self.pause_event.set()
        
    def safe_callback(self, event_type, data):
        """Thread-safe callback wrapper for GUI updates"""
        if self.callback:
            with self.callback_lock:
                try:
                    self.callback(event_type, data)
                except Exception as e:
                    # Log callback errors but don't crash the crawler
                    self.log('ERROR', f"Callback error: {str(e)}")
    
    def log(self, level, message):
        """Send log message via callback"""
        if self.callback:
            self.safe_callback('log', {
                'level': level,
                'message': message
            })
            
    def format_size(self, size_bytes):
        """Format file size in human-readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"

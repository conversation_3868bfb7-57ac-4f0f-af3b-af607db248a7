#!/usr/bin/env python3
"""
Setup script for Advanced Image Crawler
Installs all required dependencies
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages from requirements.txt"""
    print("Installing required packages...")
    print("-" * 50)
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        # Try to install pywin32 for Windows clipboard support
        if sys.platform == "win32":
            print("\nInstalling Windows-specific packages...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pywin32"])
                print("✓ Clipboard support installed")
            except Exception as e:
                print(f"⚠ Clipboard support could not be installed (optional): {e}")
        
        print("\n✓ All dependencies installed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n✗ Error installing dependencies: {e}")
        return False

def check_python_version():
    """Check if Python version is 3.8 or higher"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"✗ Python 3.8 or higher is required. You have Python {version.major}.{version.minor}")
        return False
    print(f"✓ Python {version.major}.{version.minor} detected")
    return True

def main():
    print("Advanced Image Crawler - Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if install_requirements():
        print("\n" + "=" * 50)
        print("Setup completed successfully!")
        print("\nTo run the Image Crawler:")
        print("  python image_crawler_app.py")
        print("\nOr on Windows, double-click:")
        print("  run_image_crawler.bat")
    else:
        print("\nSetup failed. Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
